import {errorType} from '../../../configurations/errorType';
import {spawnProcess} from '../../../processes/spawnProcess';
import {Command, Source} from '../../../types';

const source = Source.PLAYSTATION_SALES;
const originId = 'test-mock';
const command = Command.SCRAPE;
const params = [command, `--source=${source}`];
const expectedResult = {hello: 'reksio'};
const timestamp = '2022-08-04 11:08:06,214';
const expectedOutput = [
    {timestamp, type: 'output', message: 'Downloading...', progress: 0, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Scraping in progress...', logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'This message goes to stderr - 0', logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Downloading...', progress: 10, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Downloading...', progress: 20, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Scraping in progress...', logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Downloading...', progress: 30, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'This message goes to stderr - 3', logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Downloading...', progress: 40, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Scraping in progress...', logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Downloading...', progress: 50, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Downloading...', progress: 60, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Scraping in progress...', logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'output', message: 'Download finished!', progress: 100, logLevel: 1, authenticatedUserId: 'test-user', version: 2},
    {timestamp, type: 'result', data: expectedResult, logLevel: 1, authenticatedUserId: 'test-user', version: 2}
].map((json) => ({source, originId, ...json}));

async function spawnProcessTest(script: string, params: string[]) {
    return await spawnProcess<null>(`./src/__tests__/integration/mocks/${script}`, params, 'fake');
}

describe('spawnProcess', () => {
    test(`correctly parse pass output and errors - Promise.all`, async () => {
        const process = await spawnProcessTest('binary_mock.sh', params);
        const [output, result] = await Promise.all([process.waitForOutput(), process.result]);
        expect(result).toEqual(expectedResult);
        expect(output).toEqual(expectedOutput);
    }, 10000);

    test(`correctly parse pass output and errors - one by one`, async () => {
        const process = await spawnProcessTest('binary_mock.sh', params);
        const output = await process.waitForOutput();
        expect(await process.result).toEqual(expectedResult);
        expect(output).toEqual(expectedOutput);
    }, 10000);

    test(`correctly parse binary errors`, async () => {
        // Given
        let wasErrorCaught = false;
        const process = await spawnProcessTest('binary_mock_with_error.sh', []);

        try {
            // When
            await process.waitForOutput();
        } catch (e) {
            // Then
            wasErrorCaught = true;
            expect(e).toHaveProperty('additionalErrorData');
            expect(e).toHaveProperty('additionalErrorData.organizationsWithMissingPermissions', [
                {name: 'test', id: 'test'},
                {name: 'test2', id: 'test2'}
            ]);
            expect(e).toHaveProperty('additionalErrorData.fullErrorAsJson');
        }
        //Fallback in case error was not caught
        expect(wasErrorCaught).toBe(true);
    });

    test(`result is available, even if process.output was not consumed`, async () => {
        const process = await spawnProcessTest('binary_mock.sh', params);
        expect(await process.result).toEqual(expectedResult);
    });

    test('failing binary without output lines should reject with error', async () => {
        const run = await spawnProcessTest('binary_mock_fail_without_output.sh', params);
        await expect(run.waitForOutput()).rejects.toThrowError(errorType.DEPENDENCY_EXECUTION_ERROR);
    });
});
