#!/bin/bash

echo '{"timestamp":"2024-08-22T13:35:49.881Z","data":{"message":"You do not have enough permissions to scrape with those credentials.","additionalErrorData":{"organizationsWithMissingPermissions":[{"name":"test","id":"test"},{"name":"test2","id":"test2"}]},"stack":"Error: Missing permissions for test,test2 organizations"},"type":"error","errorType":"MISSING_PERMISSIONS","logLevel":3,"authenticatedUserId":"test-user","originId":"test-mock","version":2}'
exit 0
