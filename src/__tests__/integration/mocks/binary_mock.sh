#!/bin/bash

produceSomeOutPut(){
    i=0;
    while [ $i -le 6 ]; do
        sleep 0.1
        progress=$((i*10))
        echo "{\"timestamp\": \"2022-08-04 11:08:06,214\", \"type\":\"output\", \"message\": \"Downloading...\", \"progress\": $progress, \"logLevel\": 1, \"authenticatedUserId\": \"test-user\", \"originId\": \"test-mock\", \"version\": 2}"
        if [ $((i%2)) -eq 0 ]; then
            echo "{\"timestamp\": \"2022-08-04 11:08:06,214\", \"type\":\"output\", \"message\": \"Scraping in progress...\", \"logLevel\": 1, \"authenticatedUserId\": \"test-user\", \"originId\": \"test-mock\", \"version\": 2}"
        fi

        if [ $((i%3)) -eq 0 ]; then
            echo "{\"timestamp\": \"2022-08-04 11:08:06,214\", \"type\":\"output\", \"message\": \"This message goes to stderr - $i\", \"logLevel\": 1, \"authenticatedUserId\": \"test-user\", \"originId\": \"test-mock\", \"version\": 2}" >&2
        fi
        i=$((i+1))
    done
    echo "{\"timestamp\": \"2022-08-04 11:08:06,214\", \"type\":\"output\", \"message\": \"Download finished!\", \"progress\": 100, \"logLevel\": 1, \"authenticatedUserId\": \"test-user\", \"originId\": \"test-mock\", \"version\": 2}"
    echo "{\"timestamp\": \"2022-08-04 11:08:06,214\", \"type\": \"result\", \"data\": {\"hello\": \"reksio\"}, \"logLevel\": 1, \"authenticatedUserId\": \"test-user\", \"originId\": \"test-mock\", \"version\": 2}"
}

produceSomeOutPut
